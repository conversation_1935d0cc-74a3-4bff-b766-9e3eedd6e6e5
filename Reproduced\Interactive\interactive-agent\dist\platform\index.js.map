{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/platform/index.ts"], "names": [], "mappings": "AAAA,oCAAoC;AACpC,OAAO,EAGL,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACpB,yBAAyB,EACzB,uBAAuB,EACvB,4BAA4B,EAC7B,MAAM,wBAAwB,CAAC;AAEhC,gCAAgC;AAChC,OAAO,EAGL,eAAe,EACf,iBAAiB,EAClB,MAAM,oBAAoB,CAAC;AAE5B,qCAAqC;AACrC,OAAO,EAGL,wBAAwB,EACxB,mBAAmB,EACnB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EACnB,MAAM,yBAAyB,CAAC;AAEjC,wBAAwB;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,eAAe,EAAe,MAAM,oBAAoB,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AAE9D;;GAEG;AACH,MAAM,UAAU,gBAAgB;IAC9B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,gBAAgB,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IACzC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC;IAClG,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iCAAiC;IACrD,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;IACxE,MAAM,YAAY,GAAG,MAAM,oBAAoB,EAAE,CAAC;IAClD,OAAO,YAAY,CAAC,kBAAkB,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,OAAiB,EAAE,EACnB,UAAqD,EAAE;IAEvD,kCAAkC;IAClC,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAEvD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;IAC5C,CAAC;IAED,kDAAkD;IAClD,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,EAAE,CAAC;IAErD,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAChC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,wDAAwD;YAC/D,YAAY,EAAE,IAAI;SACnB,CAAC;IACJ,CAAC;IAED,yCAAyC;IACzC,KAAK,MAAM,QAAQ,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;QAC1D,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,cAAc,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/D,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,OAAO,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YACnD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sCAAsC;YACtC,SAAS;QACX,CAAC;IACH,CAAC;IAED,OAAO;QACL,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,oCAAoC;QAC3C,YAAY,EAAE,IAAI;KACnB,CAAC;AACJ,CAAC;AAED,kCAAkC;AAClC,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;IACzC,yBAAyB,EAAE,IAAI;IAC/B,6BAA6B,EAAE,IAAI;IACnC,iBAAiB,EAAE,CAAC;IACpB,oBAAoB,EAAE,IAAI;CAClB,CAAC;AAKX;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,QAAQ,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC9B,KAAK,OAAO;YACV,OAAO,OAAO,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,OAAO;YACV,OAAO,OAAO,CAAC;QACjB;YACE,OAAO,OAAO,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS;IACvB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC;AACjE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAClE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,KAAK;IACnB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAcD,MAAM,UAAU,kBAAkB;IAChC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,OAAO;QACL,IAAI,EAAE,eAAe,EAAE;QACvB,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;QAC/C,UAAU,EAAE,YAAY,CAAC,UAAU;QACnC,UAAU,EAAE,YAAY,CAAC,UAAU;QACnC,cAAc,EAAE,YAAY,CAAC,KAAK;QAClC,aAAa,EAAE,YAAY,CAAC,kBAAkB,CAAC,MAAM;KACtD,CAAC;AACJ,CAAC"}