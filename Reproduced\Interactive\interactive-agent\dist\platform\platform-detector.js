import * as os from 'os';
import * as fs from 'fs';
import { spawn } from 'child_process';
// Module-level cache
let cachedPlatformInfo = null;
let cacheTimestamp = 0;
const CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes
// Cache des résultats de test de terminaux
const terminalTestCache = new Map();
/**
 * Détecte si nous sommes dans un environnement WSL
 */
function detectWSL() {
    // Vérification des variables d'environnement WSL
    if (process.env.WSL_DISTRO_NAME || process.env.WSLENV) {
        return true;
    }
    // Vérification du fichier /proc/version (Linux uniquement)
    if (os.platform() === 'linux') {
        try {
            const versionInfo = fs.readFileSync('/proc/version', 'utf8');
            return versionInfo.toLowerCase().includes('microsoft') ||
                versionInfo.toLowerCase().includes('wsl');
        }
        catch {
            // Ignore les erreurs de lecture
        }
    }
    return false;
}
/**
 * Détecte si nous sommes dans un environnement MSYS/MinGW
 */
function detectMSYS() {
    return !!(process.env.MSYSTEM || process.env.MINGW_PREFIX);
}
/**
 * Détecte le shell disponible sur le système
 */
function detectShell() {
    // Windows
    if (os.platform() === 'win32') {
        return process.env.ComSpec || 'cmd.exe';
    }
    // Unix-like systems
    if (process.env.SHELL) {
        return process.env.SHELL;
    }
    // Fallback: chercher les shells communs
    const commonShells = ['/bin/bash', '/bin/sh', '/bin/zsh', '/bin/fish'];
    for (const shell of commonShells) {
        try {
            if (fs.existsSync(shell)) {
                return shell;
            }
        }
        catch {
            // Ignore les erreurs d'accès
        }
    }
    return '/bin/sh'; // Fallback ultime
}
/**
 * Vérifie si l'environnement a un affichage graphique
 */
function hasGraphicalDisplay() {
    // Unix-like: vérifier DISPLAY
    if (process.env.DISPLAY) {
        return true;
    }
    // macOS: toujours considéré comme ayant un affichage
    if (os.platform() === 'darwin') {
        return true;
    }
    // Windows: toujours considéré comme ayant un affichage
    if (os.platform() === 'win32') {
        return true;
    }
    // Wayland sur Linux
    if (process.env.WAYLAND_DISPLAY) {
        return true;
    }
    return false;
}
/**
 * Détecte si l'environnement est headless (sans interface graphique)
 */
function isHeadlessEnvironment() {
    // Variables d'environnement CI/CD communes
    const ciEnvVars = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI', 'JENKINS_URL'];
    if (ciEnvVars.some(envVar => process.env[envVar])) {
        return true;
    }
    // SSH sans X11 forwarding
    if (process.env.SSH_CLIENT && !process.env.DISPLAY) {
        return true;
    }
    // Docker container
    if (fs.existsSync('/.dockerenv')) {
        return true;
    }
    return false;
}
/**
 * Teste si une commande est disponible sur le système
 */
async function isCommandAvailable(command) {
    if (terminalTestCache.has(command)) {
        return terminalTestCache.get(command);
    }
    return new Promise((resolve) => {
        const platform = os.platform();
        let testCommand;
        let testArgs;
        if (platform === 'win32') {
            testCommand = 'where';
            testArgs = [command];
        }
        else {
            testCommand = 'which';
            testArgs = [command];
        }
        const testProcess = spawn(testCommand, testArgs, {
            stdio: 'ignore',
            timeout: 3000
        });
        testProcess.on('close', (code) => {
            const available = code === 0;
            terminalTestCache.set(command, available);
            resolve(available);
        });
        testProcess.on('error', () => {
            terminalTestCache.set(command, false);
            resolve(false);
        });
        // Timeout de sécurité
        setTimeout(() => {
            testProcess.kill();
            terminalTestCache.set(command, false);
            resolve(false);
        }, 3000);
    });
}
/**
 * Détecte les terminaux disponibles sur le système
 */
async function detectAvailableTerminals() {
    const platform = os.platform();
    let candidateTerminals = [];
    if (platform === 'win32') {
        // Windows terminals
        candidateTerminals = [
            'wt.exe', // Windows Terminal
            'ConEmu64.exe',
            'ConEmu.exe',
            'cmd.exe',
            'powershell.exe'
        ];
    }
    else if (platform === 'darwin') {
        // macOS terminals - pour macOS, on utilise 'open' avec des applications
        candidateTerminals = ['open']; // On vérifie juste que 'open' est disponible
        // Vérification spéciale pour les applications macOS
        const macApps = [];
        if (fs.existsSync('/Applications/iTerm.app')) {
            macApps.push('iTerm.app');
        }
        if (fs.existsSync('/Applications/Utilities/Terminal.app')) {
            macApps.push('Terminal.app');
        }
        // Si 'open' est disponible et qu'on a des apps, on retourne les apps
        if (await isCommandAvailable('open') && macApps.length > 0) {
            return macApps;
        }
        return [];
    }
    else {
        // Linux terminals
        candidateTerminals = [
            'gnome-terminal',
            'konsole',
            'xfce4-terminal',
            'mate-terminal',
            'terminator',
            'xterm',
            'urxvt'
        ];
    }
    // Tester la disponibilité de chaque terminal
    const availabilityPromises = candidateTerminals.map(async (terminal) => {
        const available = await isCommandAvailable(terminal);
        return available ? terminal : null;
    });
    const results = await Promise.all(availabilityPromises);
    return results.filter((terminal) => terminal !== null);
}
/**
 * Détermine si le spawn de terminal est possible
 */
function canSpawnTerminal(platformInfo) {
    // Pas de spawn en environnement headless
    if (platformInfo.isHeadless) {
        return false;
    }
    // Pas de spawn sans affichage graphique (sauf Windows)
    if (!platformInfo.hasDisplay && os.platform() !== 'win32') {
        return false;
    }
    // Doit avoir au moins un terminal disponible
    return (platformInfo.availableTerminals?.length || 0) > 0;
}
/**
 * Obtient les informations de plateforme de manière asynchrone (avec cache)
 */
export async function getPlatformInfoAsync(forceRefresh = false) {
    const now = Date.now();
    // Utiliser le cache si disponible et valide
    if (!forceRefresh && cachedPlatformInfo && (now - cacheTimestamp) < CACHE_TTL_MS) {
        return cachedPlatformInfo;
    }
    // Détecter les informations de plateforme
    const platform = os.platform();
    const isWSL = detectWSL();
    const isMSYS = detectMSYS();
    const shell = detectShell();
    const hasDisplay = hasGraphicalDisplay();
    const isHeadless = isHeadlessEnvironment();
    const availableTerminals = await detectAvailableTerminals();
    const platformInfo = {
        platform,
        isWSL,
        isMSYS,
        shell,
        hasDisplay,
        isHeadless,
        availableTerminals,
        canSpawnTerminal: false // Sera défini ci-dessous
    };
    platformInfo.canSpawnTerminal = canSpawnTerminal(platformInfo);
    // Mettre en cache
    cachedPlatformInfo = platformInfo;
    cacheTimestamp = now;
    return platformInfo;
}
/**
 * Obtient les informations de plateforme (version synchrone avec cache)
 * Note: Cette version utilise le cache ou une détection simplifiée pour les terminaux
 */
export function getPlatformInfo(forceRefresh = false) {
    const now = Date.now();
    // Utiliser le cache si disponible et valide
    if (!forceRefresh && cachedPlatformInfo && (now - cacheTimestamp) < CACHE_TTL_MS) {
        return cachedPlatformInfo;
    }
    // Si pas de cache, créer une version avec détection simplifiée des terminaux
    const platform = os.platform();
    const isWSL = detectWSL();
    const isMSYS = detectMSYS();
    const shell = detectShell();
    const hasDisplay = hasGraphicalDisplay();
    const isHeadless = isHeadlessEnvironment();
    // Détection simplifiée des terminaux pour la version synchrone
    let availableTerminals = [];
    if (platform === 'win32') {
        availableTerminals = ['cmd.exe', 'powershell.exe']; // Toujours disponibles sur Windows
    }
    else if (platform === 'darwin') {
        availableTerminals = fs.existsSync('/Applications/Utilities/Terminal.app') ? ['Terminal.app'] : [];
    }
    else {
        // Linux - on assume qu'au moins xterm est disponible
        availableTerminals = ['xterm'];
    }
    const platformInfo = {
        platform,
        isWSL,
        isMSYS,
        shell,
        hasDisplay,
        isHeadless,
        availableTerminals,
        canSpawnTerminal: false // Sera défini ci-dessous
    };
    platformInfo.canSpawnTerminal = canSpawnTerminal(platformInfo);
    // Ne pas mettre en cache cette version simplifiée
    // Le cache sera mis à jour par getPlatformInfoAsync
    return platformInfo;
}
/**
 * Rafraîchit le cache de détection de plateforme de manière asynchrone
 */
export async function refreshPlatformCacheAsync() {
    return getPlatformInfoAsync(true);
}
/**
 * Rafraîchit le cache de détection de plateforme (version synchrone)
 */
export function refreshPlatformCache() {
    return getPlatformInfo(true);
}
/**
 * Obtient les capacités de terminal pour la plateforme actuelle de manière asynchrone
 */
export async function getTerminalCapabilitiesAsync() {
    const platformInfo = await getPlatformInfoAsync();
    return {
        canSpawn: platformInfo.canSpawnTerminal,
        preferredTerminal: platformInfo.availableTerminals[0] || null,
        fallbackTerminals: platformInfo.availableTerminals.slice(1)
    };
}
/**
 * Obtient les capacités de terminal pour la plateforme actuelle (version synchrone)
 */
export function getTerminalCapabilities() {
    const platformInfo = getPlatformInfo();
    return {
        canSpawn: platformInfo.canSpawnTerminal,
        preferredTerminal: platformInfo.availableTerminals[0] || null,
        fallbackTerminals: platformInfo.availableTerminals.slice(1)
    };
}
//# sourceMappingURL=platform-detector.js.map