// Re-exports from platform-detector
export {
  PlatformInfo,
  TerminalCapabilities,
  getPlatformInfo,
  getPlatformInfoAsync,
  refreshPlatformCache,
  refreshPlatformCacheAsync,
  getTerminalCapabilities,
  getTerminalCapabilitiesAsync
} from './platform-detector.js';

// Re-exports from process-spawn
export {
  SpawnOptions,
  SpawnResult,
  spawnInTerminal,
  testTerminalSpawn
} from './process-spawn.js';

// Re-exports from terminal-fallbacks
export {
  TerminalInfo,
  FallbackStrategy,
  detectAvailableTerminals,
  getFallbackStrategy,
  supportsGUITerminals,
  getDefaultTerminal,
  testTerminalLaunch
} from './terminal-fallbacks.js';

// Convenience functions
import { getPlatformInfo } from './platform-detector.js';
import { spawnInTerminal, SpawnResult } from './process-spawn.js';
import { getFallbackStrategy } from './terminal-fallbacks.js';

/**
 * Fonction de convenance pour vérifier si le spawn de terminal est possible
 */
export function canSpawnTerminal(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.canSpawnTerminal;
}

/**
 * Fonction de convenance pour obtenir les terminaux disponibles (avec vérification réelle)
 */
export async function getAvailableTerminals(): Promise<string[]> {
  const terminals = await import('./terminal-fallbacks.js').then(m => m.detectAvailableTerminals());
  return terminals.map((t: any) => t.name);
}

/**
 * Fonction de convenance pour obtenir les terminaux disponibles depuis platform-detector
 */
export async function getAvailableTerminalsFromPlatform(): Promise<string[]> {
  const { getPlatformInfoAsync } = await import('./platform-detector.js');
  const platformInfo = await getPlatformInfoAsync();
  return platformInfo.availableTerminals;
}

/**
 * Fonction de convenance pour détecter le shell par défaut
 */
export function detectShell(): string {
  const platformInfo = getPlatformInfo();
  return platformInfo.shell;
}

/**
 * Fonction de convenance pour spawn avec fallback automatique
 */
export async function spawnWithFallback(
  command: string,
  args: string[] = [],
  options: import('./process-spawn.js').SpawnOptions = {}
): Promise<SpawnResult & { usedFallback: boolean }> {
  // Essayer d'abord le spawn normal
  const result = spawnInTerminal(command, args, options);
  
  if (result.success) {
    return { ...result, usedFallback: false };
  }
  
  // Si échec, essayer avec la stratégie de fallback
  const fallbackStrategy = await getFallbackStrategy();
  
  if (fallbackStrategy.useConsole) {
    return {
      process: null,
      success: false,
      error: 'Terminal spawning failed, console fallback recommended',
      usedFallback: true
    };
  }
  
  // Essayer avec les terminaux de fallback
  for (const terminal of fallbackStrategy.fallbackTerminals) {
    try {
      // Adapter les options pour le terminal de fallback
      const fallbackResult = spawnInTerminal(command, args, options);
      if (fallbackResult.success) {
        return { ...fallbackResult, usedFallback: true };
      }
    } catch (error) {
      // Continuer avec le prochain terminal
      continue;
    }
  }
  
  return {
    process: null,
    success: false,
    error: 'All terminal spawn attempts failed',
    usedFallback: true
  };
}

// Constants pour la configuration
export const PLATFORM_CONSTANTS = {
  CACHE_TTL_MS: 5 * 60 * 1000, // 5 minutes
  TERMINAL_SPAWN_TIMEOUT_MS: 5000,
  PLATFORM_DETECTION_TIMEOUT_MS: 3000,
  MAX_SPAWN_RETRIES: 3,
  SPAWN_RETRY_DELAY_MS: 1000
} as const;

// Types utilitaires
export type PlatformType = 'win32' | 'darwin' | 'linux' | 'other';

/**
 * Obtient le type de plateforme simplifié
 */
export function getPlatformType(): PlatformType {
  const platformInfo = getPlatformInfo();
  
  switch (platformInfo.platform) {
    case 'win32':
      return 'win32';
    case 'darwin':
      return 'darwin';
    case 'linux':
      return 'linux';
    default:
      return 'other';
  }
}

/**
 * Vérifie si la plateforme est Windows (incluant WSL)
 */
export function isWindows(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'win32' || platformInfo.isWSL;
}

/**
 * Vérifie si la plateforme est macOS
 */
export function isMacOS(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'darwin';
}

/**
 * Vérifie si la plateforme est Linux (excluant WSL)
 */
export function isLinux(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'linux' && !platformInfo.isWSL;
}

/**
 * Vérifie si nous sommes dans WSL
 */
export function isWSL(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.isWSL;
}

/**
 * Obtient un résumé des capacités de la plateforme
 */
export interface PlatformSummary {
  type: PlatformType;
  canSpawnTerminal: boolean;
  hasDisplay: boolean;
  isHeadless: boolean;
  preferredShell: string;
  terminalCount: number;
}

export function getPlatformSummary(): PlatformSummary {
  const platformInfo = getPlatformInfo();
  
  return {
    type: getPlatformType(),
    canSpawnTerminal: platformInfo.canSpawnTerminal,
    hasDisplay: platformInfo.hasDisplay,
    isHeadless: platformInfo.isHeadless,
    preferredShell: platformInfo.shell,
    terminalCount: platformInfo.availableTerminals.length
  };
}
